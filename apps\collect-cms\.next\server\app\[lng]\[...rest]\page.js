/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[lng]/[...rest]/page";
exports.ids = ["app/[lng]/[...rest]/page"];
exports.modules = {

/***/ "(ssr)/../../packages/i18n/public/locales lazy recursive ^\\.\\/.*\\/.*\\.json$":
/*!************************************************************************************!*\
  !*** ../../packages/i18n/public/locales/ lazy ^\.\/.*\/.*\.json$ namespace object ***!
  \************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./en/common.json": [
		"(ssr)/../../packages/i18n/public/locales/en/common.json",
		"_ssr_packages_i18n_public_locales_en_common_json"
	],
	"./en/test.json": [
		"(ssr)/../../packages/i18n/public/locales/en/test.json",
		"_ssr_packages_i18n_public_locales_en_test_json"
	],
	"./vi/common.json": [
		"(ssr)/../../packages/i18n/public/locales/vi/common.json",
		"_ssr_packages_i18n_public_locales_vi_common_json"
	],
	"./vi/test.json": [
		"(ssr)/../../packages/i18n/public/locales/vi/test.json",
		"_ssr_packages_i18n_public_locales_vi_test_json"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(ssr)/../../packages/i18n/public/locales lazy recursive ^\\.\\/.*\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blng%5D%2F%5B...rest%5D%2Fpage&page=%2F%5Blng%5D%2F%5B...rest%5D%2Fpage&appPaths=%2F%5Blng%5D%2F%5B...rest%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blng%5D%2F%5B...rest%5D%2Fpage.tsx&appDir=D%3A%5CCDA%5Crepos%5Cbrand-compass-frontend-template%5Capps%5Ccollect-cms%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCDA%5Crepos%5Cbrand-compass-frontend-template%5Capps%5Ccollect-cms&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blng%5D%2F%5B...rest%5D%2Fpage&page=%2F%5Blng%5D%2F%5B...rest%5D%2Fpage&appPaths=%2F%5Blng%5D%2F%5B...rest%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blng%5D%2F%5B...rest%5D%2Fpage.tsx&appDir=D%3A%5CCDA%5Crepos%5Cbrand-compass-frontend-template%5Capps%5Ccollect-cms%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCDA%5Crepos%5Cbrand-compass-frontend-template%5Capps%5Ccollect-cms&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[lng]',\n        {\n        children: [\n        '[...rest]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[lng]/[...rest]/page.tsx */ \"(rsc)/./src/app/[lng]/[...rest]/page.tsx\")), \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\[lng]\\\\[...rest]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[lng]/layout.tsx */ \"(rsc)/./src/app/[lng]/layout.tsx\")), \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\[lng]\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[lng]/not-found.tsx */ \"(rsc)/./src/app/[lng]/not-found.tsx\")), \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\[lng]\\\\not-found.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\")), \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\not-found.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\[lng]\\\\[...rest]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/[lng]/[...rest]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[lng]/[...rest]/page\",\n        pathname: \"/[lng]/[...rest]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blng%5D%2F%5B...rest%5D%2Fpage&page=%2F%5Blng%5D%2F%5B...rest%5D%2Fpage&appPaths=%2F%5Blng%5D%2F%5B...rest%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blng%5D%2F%5B...rest%5D%2Fpage.tsx&appDir=D%3A%5CCDA%5Crepos%5Cbrand-compass-frontend-template%5Capps%5Ccollect-cms%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCDA%5Crepos%5Cbrand-compass-frontend-template%5Capps%5Ccollect-cms&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blng%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Csrc%5C%5Ccontexts%5C%5CNavigationContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Ci18n%5C%5Csrc%5C%5Cclient%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Ci18n%5C%5Csrc%5C%5Chooks%5C%5CuseLanguageSwitch.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Ci18n%5C%5Csrc%5C%5Clink%5C%5Cclient%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blng%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Csrc%5C%5Ccontexts%5C%5CNavigationContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Ci18n%5C%5Csrc%5C%5Cclient%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Ci18n%5C%5Csrc%5C%5Chooks%5C%5CuseLanguageSwitch.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Ci18n%5C%5Csrc%5C%5Clink%5C%5Cclient%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/NavigationContext.tsx */ \"(ssr)/./src/contexts/NavigationContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../packages/i18n/src/client/index.tsx */ \"(ssr)/../../packages/i18n/src/client/index.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../packages/i18n/src/hooks/useLanguageSwitch.tsx */ \"(ssr)/../../packages/i18n/src/hooks/useLanguageSwitch.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../packages/i18n/src/link/client/index.tsx */ \"(ssr)/../../packages/i18n/src/link/client/index.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5C%5Blng%5D%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Csrc%5C%5Ccontexts%5C%5CNavigationContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Ci18n%5C%5Csrc%5C%5Cclient%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Ci18n%5C%5Csrc%5C%5Chooks%5C%5CuseLanguageSwitch.tsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Ci18n%5C%5Csrc%5C%5Clink%5C%5Cclient%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Csrc%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Csrc%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(ssr)/./src/app/not-found.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNDREElNUMlNUNyZXBvcyU1QyU1Q2JyYW5kLWNvbXBhc3MtZnJvbnRlbmQtdGVtcGxhdGUlNUMlNUNhcHBzJTVDJTVDY29sbGVjdC1jbXMlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNub3QtZm91bmQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwSkFBZ0kiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2xsZWN0LWNtcy8/OTZhNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXENEQVxcXFxyZXBvc1xcXFxicmFuZC1jb21wYXNzLWZyb250ZW5kLXRlbXBsYXRlXFxcXGFwcHNcXFxcY29sbGVjdC1jbXNcXFxcc3JjXFxcXGFwcFxcXFxub3QtZm91bmQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Csrc%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.scss%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Cui-lib%5C%5Csrc%5C%5Ccontexts%5C%5CGeneralSettingContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Cui-lib%5C%5Csrc%5C%5Ccontexts%5C%5CNavigationContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Cui-lib%5C%5Csrc%5C%5Ccontexts%5C%5CQuickNavigationContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Cui-lib%5C%5Csrc%5C%5Cstyles%5C%5Cglobal.scss%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.scss%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Cui-lib%5C%5Csrc%5C%5Ccontexts%5C%5CGeneralSettingContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Cui-lib%5C%5Csrc%5C%5Ccontexts%5C%5CNavigationContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Cui-lib%5C%5Csrc%5C%5Ccontexts%5C%5CQuickNavigationContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Cui-lib%5C%5Csrc%5C%5Cstyles%5C%5Cglobal.scss%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../packages/ui-lib/src/contexts/GeneralSettingContext.tsx */ \"(ssr)/../../packages/ui-lib/src/contexts/GeneralSettingContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../packages/ui-lib/src/contexts/NavigationContext.tsx */ \"(ssr)/../../packages/ui-lib/src/contexts/NavigationContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../packages/ui-lib/src/contexts/QuickNavigationContext.tsx */ \"(ssr)/../../packages/ui-lib/src/contexts/QuickNavigationContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Capps%5C%5Ccollect-cms%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.scss%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Cui-lib%5C%5Csrc%5C%5Ccontexts%5C%5CGeneralSettingContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Cui-lib%5C%5Csrc%5C%5Ccontexts%5C%5CNavigationContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Cui-lib%5C%5Csrc%5C%5Ccontexts%5C%5CQuickNavigationContext.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCDA%5C%5Crepos%5C%5Cbrand-compass-frontend-template%5C%5Cpackages%5C%5Cui-lib%5C%5Csrc%5C%5Cstyles%5C%5Cglobal.scss%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_error__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/error */ \"(ssr)/./node_modules/next/error.js\");\n/* harmony import */ var next_error__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_error__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n// Render the default Next.js 404 page when a route\n// is requested that doesn't match the middleware and\n// therefore doesn't have a locale associated with it.\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_error__WEBPACK_IMPORTED_MODULE_1___default()), {\n                statusCode: 404\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\not-found.tsx\",\n                lineNumber: 13,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\not-found.tsx\",\n            lineNumber: 12,\n            columnNumber: 4\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\not-found.tsx\",\n        lineNumber: 11,\n        columnNumber: 3\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL25vdC1mb3VuZC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRThCO0FBRTlCLG1EQUFtRDtBQUNuRCxxREFBcUQ7QUFDckQsc0RBQXNEO0FBRXZDLFNBQVNDO0lBQ3ZCLHFCQUNDLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNWLDRFQUFDQztzQkFDQSw0RUFBQ0osbURBQUtBO2dCQUFDSyxZQUFZOzs7Ozs7Ozs7Ozs7Ozs7O0FBSXZCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29sbGVjdC1jbXMvLi9zcmMvYXBwL25vdC1mb3VuZC50c3g/Y2FlMiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IEVycm9yIGZyb20gJ25leHQvZXJyb3InXG5cbi8vIFJlbmRlciB0aGUgZGVmYXVsdCBOZXh0LmpzIDQwNCBwYWdlIHdoZW4gYSByb3V0ZVxuLy8gaXMgcmVxdWVzdGVkIHRoYXQgZG9lc24ndCBtYXRjaCB0aGUgbWlkZGxld2FyZSBhbmRcbi8vIHRoZXJlZm9yZSBkb2Vzbid0IGhhdmUgYSBsb2NhbGUgYXNzb2NpYXRlZCB3aXRoIGl0LlxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBOb3RGb3VuZCgpIHtcblx0cmV0dXJuIChcblx0XHQ8aHRtbCBsYW5nPVwiZW5cIj5cblx0XHRcdDxib2R5PlxuXHRcdFx0XHQ8RXJyb3Igc3RhdHVzQ29kZT17NDA0fSAvPlxuXHRcdFx0PC9ib2R5PlxuXHRcdDwvaHRtbD5cblx0KVxufVxuIl0sIm5hbWVzIjpbIkVycm9yIiwiTm90Rm91bmQiLCJodG1sIiwibGFuZyIsImJvZHkiLCJzdGF0dXNDb2RlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/NavigationContext.tsx":
/*!********************************************!*\
  !*** ./src/contexts/NavigationContext.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavigationContext: () => (/* binding */ NavigationContext),\n/* harmony export */   \"default\": () => (/* binding */ NavigationProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ NavigationContext,default auto */ \n\nconst GLOBAL_ROUTE = [\n    {\n        uid: \"\",\n        apiId: \"\",\n        isDisplayed: false,\n        isPinned: false,\n        info: {\n            description: undefined,\n            displayName: \"\"\n        },\n        kind: \"group\",\n        layouts: []\n    }\n];\nconst defaultContext = GLOBAL_ROUTE;\nconst NavigationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(defaultContext);\nfunction NavigationProvider({ data, children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavigationContext.Provider, {\n        value: data,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\contexts\\\\NavigationContext.tsx\",\n        lineNumber: 68,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/NavigationContext.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/i18n/src/client/index.tsx":
/*!************************************************!*\
  !*** ../../packages/i18n/src/client/index.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useClientTranslation: () => (/* binding */ useClientTranslation)\n/* harmony export */ });\n/* harmony import */ var i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! i18next */ \"(ssr)/../../node_modules/i18next/dist/esm/i18next.js\");\n/* harmony import */ var i18next_browser_languagedetector__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! i18next-browser-languagedetector */ \"(ssr)/../../node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js\");\n/* harmony import */ var i18next_resources_to_backend__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! i18next-resources-to-backend */ \"(ssr)/../../node_modules/i18next-resources-to-backend/dist/esm/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(ssr)/../../node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _settings__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../settings */ \"(ssr)/../../packages/i18n/src/settings/index.ts\");\n/* eslint-disable @typescript-eslint/no-explicit-any */ /* __next_internal_client_entry_do_not_use__ useClientTranslation auto */ \n\n\n\n\n// import LocizeBackend from 'i18next-locize-backend'\n\nconst runsOnServerSide = \"undefined\" === \"undefined\";\nlet hasInit = false;\nconst initialize = ()=>{\n    if (hasInit) {\n        return;\n    }\n    hasInit = true;\n    // on client side the normal singleton is ok\n    i18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"].use(react_i18next__WEBPACK_IMPORTED_MODULE_4__.initReactI18next).use(i18next_browser_languagedetector__WEBPACK_IMPORTED_MODULE_1__[\"default\"]).use((0,i18next_resources_to_backend__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((language, namespace)=>__webpack_require__(\"(ssr)/../../packages/i18n/public/locales lazy recursive ^\\\\.\\\\/.*\\\\/.*\\\\.json$\")(`./${language}/${namespace}.json`)))// .use(LocizeBackend) // locize backend could be used on client side, but prefer to keep it in sync with server side\n    .init({\n        ...(0,_settings__WEBPACK_IMPORTED_MODULE_5__.getOptions)(),\n        lng: undefined,\n        detection: {\n            order: [\n                \"path\",\n                \"htmlTag\",\n                \"cookie\",\n                \"navigator\"\n            ]\n        },\n        preload: runsOnServerSide ? _settings__WEBPACK_IMPORTED_MODULE_5__.languages : []\n    });\n};\nfunction useClientTranslation(lng, ns = _settings__WEBPACK_IMPORTED_MODULE_5__.defaultNS, options = {}) {\n    initialize();\n    const ret = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)(ns, options);\n    const { i18n } = ret;\n    if (runsOnServerSide && i18n.resolvedLanguage !== lng) {\n        i18n.changeLanguage(lng);\n    } else {\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n            if (i18n.resolvedLanguage === lng) return;\n            i18n.changeLanguage(lng);\n        }, [\n            lng,\n            i18n\n        ]);\n    }\n    return ret;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/i18n/src/client/index.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/i18n/src/hooks/useLanguageSwitch.tsx":
/*!***********************************************************!*\
  !*** ../../packages/i18n/src/hooks/useLanguageSwitch.tsx ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLanguageChanger: () => (/* binding */ useLanguageChanger)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_setCookie_collective_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=setCookie!=!@collective/core */ \"(ssr)/../../packages/core/dist/utils/cookies.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _settings__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../settings */ \"(ssr)/../../packages/i18n/src/settings/index.ts\");\n/* __next_internal_client_entry_do_not_use__ useLanguageChanger auto */ \n\n\nfunction useLanguageChanger() {\n    const path = (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.usePathname)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n    return (lng)=>{\n        (0,_barrel_optimize_names_setCookie_collective_core__WEBPACK_IMPORTED_MODULE_1__.setCookie)(\"NEXT_LOCALE\", lng, 365);\n        if (_settings__WEBPACK_IMPORTED_MODULE_2__.languages.some((v)=>path.split(\"/\")[1] === v)) {\n            // Remove the language prefix from the path\n            // eg: \"/en\", \"/vi\"\n            // If path is index page, redirect to the root path\n            const destPath = path.length > 3 ? path.slice(3) : \"/\";\n            router.push(`${destPath}?lng=${lng}`);\n        } else {\n            router.push(`${path}?lng=${lng}`);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/i18n/src/hooks/useLanguageSwitch.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/i18n/src/link/client/index.tsx":
/*!*****************************************************!*\
  !*** ../../packages/i18n/src/link/client/index.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientLink: () => (/* binding */ ClientLink)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* eslint-disable @typescript-eslint/naming-convention */ /* __next_internal_client_entry_do_not_use__ ClientLink auto */ \n\n\nconst ClientLink = ({ children, href, prefetch, replace, className, ...rest })=>{\n    const { lng } = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        href: `/${lng}${href}`,\n        prefetch: prefetch,\n        replace: replace,\n        className: className,\n        ...rest,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\i18n\\\\src\\\\link\\\\client\\\\index.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvaTE4bi9zcmMvbGluay9jbGllbnQvaW5kZXgudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUEsdURBQXVEO0FBRzNCO0FBQ2U7QUFHcEMsTUFBTUUsYUFBYSxDQUFDLEVBQzFCQyxRQUFRLEVBQ1JDLElBQUksRUFDSkMsUUFBUSxFQUNSQyxPQUFPLEVBQ1BDLFNBQVMsRUFDVCxHQUFHQyxNQU9IO0lBQ0EsTUFBTSxFQUFFQyxHQUFHLEVBQUUsR0FBR1IsMERBQVNBO0lBQ3pCLHFCQUNDLDhEQUFDRCxpREFBSUE7UUFDSkksTUFBTSxDQUFDLENBQUMsRUFBRUssSUFBSSxFQUFFTCxLQUFLLENBQUM7UUFDdEJDLFVBQVVBO1FBQ1ZDLFNBQVNBO1FBQ1RDLFdBQVdBO1FBQ1YsR0FBR0MsSUFBSTtrQkFFUEw7Ozs7OztBQUdKLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2xsZWN0LWNtcy8uLi8uLi9wYWNrYWdlcy9pMThuL3NyYy9saW5rL2NsaWVudC9pbmRleC50c3g/ZTA4NiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiBlc2xpbnQtZGlzYWJsZSBAdHlwZXNjcmlwdC1lc2xpbnQvbmFtaW5nLWNvbnZlbnRpb24gKi9cbid1c2UgY2xpZW50J1xuXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnXG5pbXBvcnQgeyB1c2VQYXJhbXMgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXG5pbXBvcnQgdHlwZSB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0J1xuXG5leHBvcnQgY29uc3QgQ2xpZW50TGluayA9ICh7XG5cdGNoaWxkcmVuLFxuXHRocmVmLFxuXHRwcmVmZXRjaCxcblx0cmVwbGFjZSxcblx0Y2xhc3NOYW1lLFxuXHQuLi5yZXN0XG59OiB7XG5cdGhyZWY6IHN0cmluZ1xuXHRjaGlsZHJlbjogUmVhY3ROb2RlXG5cdHByZWZldGNoPzogYm9vbGVhblxuXHRyZXBsYWNlPzogYm9vbGVhblxuXHRjbGFzc05hbWU/OiBzdHJpbmdcbn0pID0+IHtcblx0Y29uc3QgeyBsbmcgfSA9IHVzZVBhcmFtcygpXG5cdHJldHVybiAoXG5cdFx0PExpbmtcblx0XHRcdGhyZWY9e2AvJHtsbmd9JHtocmVmfWB9XG5cdFx0XHRwcmVmZXRjaD17cHJlZmV0Y2h9XG5cdFx0XHRyZXBsYWNlPXtyZXBsYWNlfVxuXHRcdFx0Y2xhc3NOYW1lPXtjbGFzc05hbWV9XG5cdFx0XHR7Li4ucmVzdH1cblx0XHQ+XG5cdFx0XHR7Y2hpbGRyZW59XG5cdFx0PC9MaW5rPlxuXHQpXG59XG4iXSwibmFtZXMiOlsiTGluayIsInVzZVBhcmFtcyIsIkNsaWVudExpbmsiLCJjaGlsZHJlbiIsImhyZWYiLCJwcmVmZXRjaCIsInJlcGxhY2UiLCJjbGFzc05hbWUiLCJyZXN0IiwibG5nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../packages/i18n/src/link/client/index.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/i18n/src/settings/index.ts":
/*!*************************************************!*\
  !*** ../../packages/i18n/src/settings/index.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultNS: () => (/* binding */ defaultNS),\n/* harmony export */   fallbackLng: () => (/* binding */ fallbackLng),\n/* harmony export */   getOptions: () => (/* binding */ getOptions),\n/* harmony export */   languages: () => (/* binding */ languages)\n/* harmony export */ });\n/* eslint-disable @typescript-eslint/naming-convention */ const fallbackLng = \"en\";\nconst languages = [\n    fallbackLng\n];\nconst defaultNS = \"common\";\nfunction getOptions(lng = fallbackLng, ns = defaultNS) {\n    return {\n        // debug: true,\n        supportedLngs: languages,\n        preload: languages,\n        fallbackLng,\n        lng,\n        fallbackNS: defaultNS,\n        defaultNS: Array.isArray(ns) ? ns[0] : ns,\n        ns\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvaTE4bi9zcmMvc2V0dGluZ3MvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLHVEQUF1RCxHQUNoRCxNQUFNQSxjQUFjLEtBQUk7QUFDeEIsTUFBTUMsWUFBWTtJQUFDRDtDQUFZO0FBQy9CLE1BQU1FLFlBQVksU0FBUTtBQUUxQixTQUFTQyxXQUFXQyxNQUFNSixXQUFXLEVBQUVLLEtBQXdCSCxTQUFTO0lBQzlFLE9BQU87UUFDTixlQUFlO1FBQ2ZJLGVBQWVMO1FBQ2ZNLFNBQVNOO1FBQ1REO1FBQ0FJO1FBQ0FJLFlBQVlOO1FBQ1pBLFdBQVdPLE1BQU1DLE9BQU8sQ0FBQ0wsTUFBTUEsRUFBRSxDQUFDLEVBQUUsR0FBR0E7UUFDdkNBO0lBQ0Q7QUFDRCIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL2kxOG4vc3JjL3NldHRpbmdzL2luZGV4LnRzPzU1M2UiXSwic291cmNlc0NvbnRlbnQiOlsiLyogZXNsaW50LWRpc2FibGUgQHR5cGVzY3JpcHQtZXNsaW50L25hbWluZy1jb252ZW50aW9uICovXG5leHBvcnQgY29uc3QgZmFsbGJhY2tMbmcgPSAnZW4nXG5leHBvcnQgY29uc3QgbGFuZ3VhZ2VzID0gW2ZhbGxiYWNrTG5nXVxuZXhwb3J0IGNvbnN0IGRlZmF1bHROUyA9ICdjb21tb24nXG5cbmV4cG9ydCBmdW5jdGlvbiBnZXRPcHRpb25zKGxuZyA9IGZhbGxiYWNrTG5nLCBuczogc3RyaW5nIHwgc3RyaW5nW10gPSBkZWZhdWx0TlMpIHtcblx0cmV0dXJuIHtcblx0XHQvLyBkZWJ1ZzogdHJ1ZSxcblx0XHRzdXBwb3J0ZWRMbmdzOiBsYW5ndWFnZXMsXG5cdFx0cHJlbG9hZDogbGFuZ3VhZ2VzLFxuXHRcdGZhbGxiYWNrTG5nLFxuXHRcdGxuZyxcblx0XHRmYWxsYmFja05TOiBkZWZhdWx0TlMsXG5cdFx0ZGVmYXVsdE5TOiBBcnJheS5pc0FycmF5KG5zKSA/IG5zWzBdIDogbnMsXG5cdFx0bnMsXG5cdH1cbn1cbiJdLCJuYW1lcyI6WyJmYWxsYmFja0xuZyIsImxhbmd1YWdlcyIsImRlZmF1bHROUyIsImdldE9wdGlvbnMiLCJsbmciLCJucyIsInN1cHBvcnRlZExuZ3MiLCJwcmVsb2FkIiwiZmFsbGJhY2tOUyIsIkFycmF5IiwiaXNBcnJheSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../packages/i18n/src/settings/index.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/contexts/GeneralSettingContext.tsx":
/*!********************************************************************!*\
  !*** ../../packages/ui-lib/src/contexts/GeneralSettingContext.tsx ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GeneralSettingContext: () => (/* binding */ GeneralSettingContext),\n/* harmony export */   \"default\": () => (/* binding */ GeneralSettingProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ GeneralSettingContext,default auto */ \n\nconst defaultContext = {\n    locale: \"en\",\n    Social: []\n};\nconst GeneralSettingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(defaultContext);\nfunction GeneralSettingProvider({ data, children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GeneralSettingContext.Provider, {\n        value: data,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\contexts\\\\GeneralSettingContext.tsx\",\n        lineNumber: 20,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9jb250ZXh0cy9HZW5lcmFsU2V0dGluZ0NvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFHcUM7QUFFckMsTUFBTUMsaUJBQXNDO0lBQzNDQyxRQUFRO0lBQ1JDLFFBQVEsRUFBRTtBQUNYO0FBRU8sTUFBTUMsc0NBQXdCSixvREFBYUEsQ0FBQ0MsZ0JBQWU7QUFFbkQsU0FBU0ksdUJBQXVCLEVBQzlDQyxJQUFJLEVBQ0pDLFFBQVEsRUFJUjtJQUNBLHFCQUFPLDhEQUFDSCxzQkFBc0JJLFFBQVE7UUFBQ0MsT0FBT0g7a0JBQU9DOzs7Ozs7QUFDdEQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2xsZWN0LWNtcy8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2NvbnRleHRzL0dlbmVyYWxTZXR0aW5nQ29udGV4dC50c3g/MzJmOSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHR5cGUgeyBHZW5lcmFsU2V0dGluZ1Byb3BzIH0gZnJvbSAnQGNvbGxlY3RpdmUvaW50ZWdyYXRpb24tbGliL3NlYXJjaCdcbmltcG9ydCB7IGNyZWF0ZUNvbnRleHQgfSBmcm9tICdyZWFjdCdcblxuY29uc3QgZGVmYXVsdENvbnRleHQ6IEdlbmVyYWxTZXR0aW5nUHJvcHMgPSB7XG5cdGxvY2FsZTogJ2VuJyxcblx0U29jaWFsOiBbXSxcbn1cblxuZXhwb3J0IGNvbnN0IEdlbmVyYWxTZXR0aW5nQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQoZGVmYXVsdENvbnRleHQpXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEdlbmVyYWxTZXR0aW5nUHJvdmlkZXIoe1xuXHRkYXRhLFxuXHRjaGlsZHJlbixcbn06IHtcblx0ZGF0YTogR2VuZXJhbFNldHRpbmdQcm9wc1xuXHRjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG5cdHJldHVybiA8R2VuZXJhbFNldHRpbmdDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXtkYXRhfT57Y2hpbGRyZW59PC9HZW5lcmFsU2V0dGluZ0NvbnRleHQuUHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiY3JlYXRlQ29udGV4dCIsImRlZmF1bHRDb250ZXh0IiwibG9jYWxlIiwiU29jaWFsIiwiR2VuZXJhbFNldHRpbmdDb250ZXh0IiwiR2VuZXJhbFNldHRpbmdQcm92aWRlciIsImRhdGEiLCJjaGlsZHJlbiIsIlByb3ZpZGVyIiwidmFsdWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/contexts/GeneralSettingContext.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/contexts/NavigationContext.tsx":
/*!****************************************************************!*\
  !*** ../../packages/ui-lib/src/contexts/NavigationContext.tsx ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavigationContext: () => (/* binding */ NavigationContext),\n/* harmony export */   \"default\": () => (/* binding */ NavigationProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ NavigationContext,default auto */ \n\nconst GLOBAL_ROUTE = [\n    {\n        label: \"Our Podcast\",\n        path: \"/our-podcast\",\n        isLocked: false\n    },\n    {\n        label: \"Work with us\",\n        path: \"/work-with-us\",\n        isLocked: false\n    },\n    {\n        label: \"Newsletters\",\n        path: \"/newsletters\",\n        isLocked: false\n    }\n];\nconst defaultContext = GLOBAL_ROUTE;\nconst NavigationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(defaultContext);\nfunction NavigationProvider({ data, children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavigationContext.Provider, {\n        value: data,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\contexts\\\\NavigationContext.tsx\",\n        lineNumber: 35,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/contexts/NavigationContext.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui-lib/src/contexts/QuickNavigationContext.tsx":
/*!*********************************************************************!*\
  !*** ../../packages/ui-lib/src/contexts/QuickNavigationContext.tsx ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuickNavigationContext: () => (/* binding */ QuickNavigationContext),\n/* harmony export */   \"default\": () => (/* binding */ NavigationProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ QuickNavigationContext,default auto */ \n\nconst GLOBAL_ROUTE = [\n    {\n        label: \"Terms and Services\",\n        path: \"/terms-of-services\",\n        isLocked: false\n    },\n    {\n        label: \"Privacy\",\n        path: \"/privacy\",\n        isLocked: false\n    }\n];\nconst defaultContext = GLOBAL_ROUTE;\nconst QuickNavigationContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(defaultContext);\nfunction NavigationProvider({ data, children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(QuickNavigationContext.Provider, {\n        value: data,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\packages\\\\ui-lib\\\\src\\\\contexts\\\\QuickNavigationContext.tsx\",\n        lineNumber: 30,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9jb250ZXh0cy9RdWlja05hdmlnYXRpb25Db250ZXh0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBR3FDO0FBRXJDLE1BQU1DLGVBQWU7SUFDcEI7UUFDQ0MsT0FBTztRQUNQQyxNQUFNO1FBQ05DLFVBQVU7SUFDWDtJQUNBO1FBQ0NGLE9BQU87UUFDUEMsTUFBTTtRQUNOQyxVQUFVO0lBQ1g7Q0FDQTtBQUVELE1BQU1DLGlCQUFxQ0o7QUFFcEMsTUFBTUssdUNBQXlCTixvREFBYUEsQ0FBQ0ssZ0JBQWU7QUFFcEQsU0FBU0UsbUJBQW1CLEVBQzFDQyxJQUFJLEVBQ0pDLFFBQVEsRUFJUjtJQUNBLHFCQUFPLDhEQUFDSCx1QkFBdUJJLFFBQVE7UUFBQ0MsT0FBT0g7a0JBQU9DOzs7Ozs7QUFDdkQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2xsZWN0LWNtcy8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL2NvbnRleHRzL1F1aWNrTmF2aWdhdGlvbkNvbnRleHQudHN4P2Y1N2YiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB0eXBlIHsgSU5hdmlnYXRpb25Qcm9wcyB9IGZyb20gJ0Bjb2xsZWN0aXZlL2ludGVncmF0aW9uLWxpYi9jbXMnXG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0IH0gZnJvbSAncmVhY3QnXG5cbmNvbnN0IEdMT0JBTF9ST1VURSA9IFtcblx0e1xuXHRcdGxhYmVsOiAnVGVybXMgYW5kIFNlcnZpY2VzJyxcblx0XHRwYXRoOiAnL3Rlcm1zLW9mLXNlcnZpY2VzJyxcblx0XHRpc0xvY2tlZDogZmFsc2UsXG5cdH0sXG5cdHtcblx0XHRsYWJlbDogJ1ByaXZhY3knLFxuXHRcdHBhdGg6ICcvcHJpdmFjeScsXG5cdFx0aXNMb2NrZWQ6IGZhbHNlLFxuXHR9LFxuXVxuXG5jb25zdCBkZWZhdWx0Q29udGV4dDogSU5hdmlnYXRpb25Qcm9wc1tdID0gR0xPQkFMX1JPVVRFXG5cbmV4cG9ydCBjb25zdCBRdWlja05hdmlnYXRpb25Db250ZXh0ID0gY3JlYXRlQ29udGV4dChkZWZhdWx0Q29udGV4dClcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTmF2aWdhdGlvblByb3ZpZGVyKHtcblx0ZGF0YSxcblx0Y2hpbGRyZW4sXG59OiB7XG5cdGRhdGE6IElOYXZpZ2F0aW9uUHJvcHNbXVxuXHRjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG5cdHJldHVybiA8UXVpY2tOYXZpZ2F0aW9uQ29udGV4dC5Qcm92aWRlciB2YWx1ZT17ZGF0YX0+e2NoaWxkcmVufTwvUXVpY2tOYXZpZ2F0aW9uQ29udGV4dC5Qcm92aWRlcj5cbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVDb250ZXh0IiwiR0xPQkFMX1JPVVRFIiwibGFiZWwiLCJwYXRoIiwiaXNMb2NrZWQiLCJkZWZhdWx0Q29udGV4dCIsIlF1aWNrTmF2aWdhdGlvbkNvbnRleHQiLCJOYXZpZ2F0aW9uUHJvdmlkZXIiLCJkYXRhIiwiY2hpbGRyZW4iLCJQcm92aWRlciIsInZhbHVlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui-lib/src/contexts/QuickNavigationContext.tsx\n");

/***/ }),

/***/ "(rsc)/./src/styles/globals.scss":
/*!*********************************!*\
  !*** ./src/styles/globals.scss ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"c6e438c2de43\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsb2JhbHMuc2NzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4vc3JjL3N0eWxlcy9nbG9iYWxzLnNjc3M/YzRmMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImM2ZTQzOGMyZGU0M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/globals.scss\n");

/***/ }),

/***/ "(rsc)/../../packages/ui-lib/src/styles/global.scss":
/*!****************************************************!*\
  !*** ../../packages/ui-lib/src/styles/global.scss ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"79eccf42ccf4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vcGFja2FnZXMvdWktbGliL3NyYy9zdHlsZXMvZ2xvYmFsLnNjc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2xsZWN0LWNtcy8uLi8uLi9wYWNrYWdlcy91aS1saWIvc3JjL3N0eWxlcy9nbG9iYWwuc2Nzcz82NzJlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNzllY2NmNDJjY2Y0XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../packages/ui-lib/src/styles/global.scss\n");

/***/ }),

/***/ "(rsc)/./src/app/[lng]/[...rest]/page.tsx":
/*!******************************************!*\
  !*** ./src/app/[lng]/[...rest]/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CatchAllPage)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n\n// export const runtime = 'edge'\nfunction CatchAllPage() {\n    (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.notFound)();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL1tsbmddL1suLi5yZXN0XS9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQztBQUUxQyxnQ0FBZ0M7QUFDakIsU0FBU0M7SUFDdkJELHlEQUFRQTtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29sbGVjdC1jbXMvLi9zcmMvYXBwL1tsbmddL1suLi5yZXN0XS9wYWdlLnRzeD9kMTNlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG5vdEZvdW5kIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJ1xuXG4vLyBleHBvcnQgY29uc3QgcnVudGltZSA9ICdlZGdlJ1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQ2F0Y2hBbGxQYWdlKCkge1xuXHRub3RGb3VuZCgpXG59XG4iXSwibmFtZXMiOlsibm90Rm91bmQiLCJDYXRjaEFsbFBhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[lng]/[...rest]/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/[lng]/layout.tsx":
/*!**********************************!*\
  !*** ./src/app/[lng]/layout.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   generateStaticParams: () => (/* binding */ generateStaticParams),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_lng_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\[lng]\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\[lng]\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_lng_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_lng_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _collective_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @collective/i18n */ \"(rsc)/../../packages/i18n/src/settings/index.ts\");\n/* harmony import */ var i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! i18next */ \"(rsc)/../../node_modules/i18next/dist/esm/i18next.js\");\n/* harmony import */ var _contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/NavigationContext */ \"(rsc)/./src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _mock_Navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/mock/Navigation */ \"(rsc)/./src/mock/Navigation.ts\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nasync function generateStaticParams() {\n    return _collective_i18n__WEBPACK_IMPORTED_MODULE_2__.languages.map((lng)=>({\n            lng\n        }));\n}\nasync function RootLayout({ children, params: { lng } }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: lng,\n        dir: (0,i18next__WEBPACK_IMPORTED_MODULE_1__.dir)(lng),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_lng_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                data: _mock_Navigation__WEBPACK_IMPORTED_MODULE_5__.NavigationData,\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\[lng]\\\\layout.tsx\",\n                lineNumber: 27,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\[lng]\\\\layout.tsx\",\n            lineNumber: 26,\n            columnNumber: 4\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\[lng]\\\\layout.tsx\",\n        lineNumber: 25,\n        columnNumber: 3\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[lng]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/[lng]/not-found.tsx":
/*!*************************************!*\
  !*** ./src/app/[lng]/not-found.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// export const runtime = 'edge'\nfunction NotFound() {\n    const headersList = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)();\n    const locale = headersList.get(\"x-next-i18n-router-locale\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                children: \"404\"\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\[lng]\\\\not-found.tsx\",\n                lineNumber: 9,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                className: \"coeus__heading\",\n                children: \"Oops, this page can’t be found.\"\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\[lng]\\\\not-found.tsx\",\n                lineNumber: 10,\n                columnNumber: 4\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\[lng]\\\\not-found.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL1tsbmddL25vdC1mb3VuZC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBc0M7QUFFdEMsZ0NBQWdDO0FBQ2pCLFNBQVNDO0lBQ3ZCLE1BQU1DLGNBQWNGLHFEQUFPQTtJQUMzQixNQUFNRyxTQUFTRCxZQUFZRSxHQUFHLENBQUM7SUFDL0IscUJBQ0MsOERBQUNDOzswQkFDQSw4REFBQ0M7MEJBQUc7Ozs7OzswQkFDSiw4REFBQ0M7Z0JBQUdDLFdBQVU7MEJBQWlCOzs7Ozs7Ozs7Ozs7QUFHbEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2xsZWN0LWNtcy8uL3NyYy9hcHAvW2xuZ10vbm90LWZvdW5kLnRzeD8zN2VlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGhlYWRlcnMgfSBmcm9tICduZXh0L2hlYWRlcnMnXG5cbi8vIGV4cG9ydCBjb25zdCBydW50aW1lID0gJ2VkZ2UnXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBOb3RGb3VuZCgpIHtcblx0Y29uc3QgaGVhZGVyc0xpc3QgPSBoZWFkZXJzKClcblx0Y29uc3QgbG9jYWxlID0gaGVhZGVyc0xpc3QuZ2V0KCd4LW5leHQtaTE4bi1yb3V0ZXItbG9jYWxlJylcblx0cmV0dXJuIChcblx0XHQ8ZGl2PlxuXHRcdFx0PGgxPjQwNDwvaDE+XG5cdFx0XHQ8aDQgY2xhc3NOYW1lPVwiY29ldXNfX2hlYWRpbmdcIj5Pb3BzLCB0aGlzIHBhZ2UgY2Fu4oCZdCBiZSBmb3VuZC48L2g0PlxuXHRcdDwvZGl2PlxuXHQpXG59XG4iXSwibmFtZXMiOlsiaGVhZGVycyIsIk5vdEZvdW5kIiwiaGVhZGVyc0xpc3QiLCJsb2NhbGUiLCJnZXQiLCJkaXYiLCJoMSIsImg0IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[lng]/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_scss__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.scss */ \"(rsc)/./src/styles/globals.scss\");\n/* harmony import */ var _collective_ui_lib_styles_global_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @collective/ui-lib/styles/global.scss */ \"(rsc)/../../packages/ui-lib/src/styles/global.scss\");\n/* harmony import */ var _collective_ui_lib_contexts_GeneralSettingContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @collective/ui-lib/contexts/GeneralSettingContext */ \"(rsc)/../../packages/ui-lib/src/contexts/GeneralSettingContext.tsx\");\n/* harmony import */ var _collective_ui_lib_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @collective/ui-lib/contexts/NavigationContext */ \"(rsc)/../../packages/ui-lib/src/contexts/NavigationContext.tsx\");\n/* harmony import */ var _collective_ui_lib_contexts_QuickNavigationContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @collective/ui-lib/contexts/QuickNavigationContext */ \"(rsc)/../../packages/ui-lib/src/contexts/QuickNavigationContext.tsx\");\n\n\n\n\n\n\nasync function RootLayout({ children }) {\n    // const [generalSetting, nav, quickNav] = await Promise.all([\n    // \tsearchGeneralSetting(),\n    // \tgetNavigationData(),\n    // \tgetNavigationData('2'),\n    // ])\n    const [generalSetting, nav, quickNav] = [\n        {\n            hits: [\n                {}\n            ]\n        },\n        [],\n        []\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_collective_ui_lib_contexts_GeneralSettingContext__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        data: generalSetting.hits[0],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_collective_ui_lib_contexts_NavigationContext__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            data: nav,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_collective_ui_lib_contexts_QuickNavigationContext__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                data: quickNav,\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 4\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 3\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQThCO0FBQ2dCO0FBR3dDO0FBQ1I7QUFDUztBQUV4RSxlQUFlRyxXQUFXLEVBQUVDLFFBQVEsRUFBaUM7SUFDbkYsOERBQThEO0lBQzlELDJCQUEyQjtJQUMzQix3QkFBd0I7SUFDeEIsMkJBQTJCO0lBQzNCLEtBQUs7SUFDTCxNQUFNLENBQUNDLGdCQUFnQkMsS0FBS0MsU0FBUyxHQUFHO1FBQUM7WUFBRUMsTUFBTTtnQkFBQyxDQUFDO2FBQUU7UUFBQztRQUFHLEVBQUU7UUFBRSxFQUFFO0tBQUM7SUFDaEUscUJBQ0MsOERBQUNSLHlGQUFzQkE7UUFBQ1MsTUFBTUosZUFBZUcsSUFBSSxDQUFDLEVBQUU7a0JBQ25ELDRFQUFDUCxxRkFBa0JBO1lBQUNRLE1BQU1IO3NCQUN6Qiw0RUFBQ0osMEZBQXNCQTtnQkFBQ08sTUFBTUY7MEJBQVdIOzs7Ozs7Ozs7Ozs7Ozs7O0FBSTdDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29sbGVjdC1jbXMvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJ0Avc3R5bGVzL2dsb2JhbHMuc2NzcydcbmltcG9ydCAnQGNvbGxlY3RpdmUvdWktbGliL3N0eWxlcy9nbG9iYWwuc2NzcydcbmltcG9ydCB7IGdldE5hdmlnYXRpb25EYXRhIH0gZnJvbSAnQGNvbGxlY3RpdmUvaW50ZWdyYXRpb24tbGliL2NtcydcbmltcG9ydCB7IHNlYXJjaEdlbmVyYWxTZXR0aW5nLCB0eXBlIEdlbmVyYWxTZXR0aW5nUHJvcHMgfSBmcm9tICdAY29sbGVjdGl2ZS9pbnRlZ3JhdGlvbi1saWIvc2VhcmNoJ1xuaW1wb3J0IEdlbmVyYWxTZXR0aW5nUHJvdmlkZXIgZnJvbSAnQGNvbGxlY3RpdmUvdWktbGliL2NvbnRleHRzL0dlbmVyYWxTZXR0aW5nQ29udGV4dCdcbmltcG9ydCBOYXZpZ2F0aW9uUHJvdmlkZXIgZnJvbSAnQGNvbGxlY3RpdmUvdWktbGliL2NvbnRleHRzL05hdmlnYXRpb25Db250ZXh0J1xuaW1wb3J0IFF1aWNrTmF2aWdhdGlvbkNvbnRleHQgZnJvbSAnQGNvbGxlY3RpdmUvdWktbGliL2NvbnRleHRzL1F1aWNrTmF2aWdhdGlvbkNvbnRleHQnXG5cbmV4cG9ydCBkZWZhdWx0IGFzeW5jIGZ1bmN0aW9uIFJvb3RMYXlvdXQoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuXHQvLyBjb25zdCBbZ2VuZXJhbFNldHRpbmcsIG5hdiwgcXVpY2tOYXZdID0gYXdhaXQgUHJvbWlzZS5hbGwoW1xuXHQvLyBcdHNlYXJjaEdlbmVyYWxTZXR0aW5nKCksXG5cdC8vIFx0Z2V0TmF2aWdhdGlvbkRhdGEoKSxcblx0Ly8gXHRnZXROYXZpZ2F0aW9uRGF0YSgnMicpLFxuXHQvLyBdKVxuXHRjb25zdCBbZ2VuZXJhbFNldHRpbmcsIG5hdiwgcXVpY2tOYXZdID0gW3sgaGl0czogW3t9XSB9LCBbXSwgW11dXG5cdHJldHVybiAoXG5cdFx0PEdlbmVyYWxTZXR0aW5nUHJvdmlkZXIgZGF0YT17Z2VuZXJhbFNldHRpbmcuaGl0c1swXSBhcyBHZW5lcmFsU2V0dGluZ1Byb3BzfT5cblx0XHRcdDxOYXZpZ2F0aW9uUHJvdmlkZXIgZGF0YT17bmF2fT5cblx0XHRcdFx0PFF1aWNrTmF2aWdhdGlvbkNvbnRleHQgZGF0YT17cXVpY2tOYXZ9PntjaGlsZHJlbn08L1F1aWNrTmF2aWdhdGlvbkNvbnRleHQ+XG5cdFx0XHQ8L05hdmlnYXRpb25Qcm92aWRlcj5cblx0XHQ8L0dlbmVyYWxTZXR0aW5nUHJvdmlkZXI+XG5cdClcbn1cbiJdLCJuYW1lcyI6WyJHZW5lcmFsU2V0dGluZ1Byb3ZpZGVyIiwiTmF2aWdhdGlvblByb3ZpZGVyIiwiUXVpY2tOYXZpZ2F0aW9uQ29udGV4dCIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImdlbmVyYWxTZXR0aW5nIiwibmF2IiwicXVpY2tOYXYiLCJoaXRzIiwiZGF0YSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\CDA\repos\brand-compass-frontend-template\apps\collect-cms\src\app\not-found.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/contexts/NavigationContext.tsx":
/*!********************************************!*\
  !*** ./src/contexts/NavigationContext.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NavigationContext: () => (/* binding */ e0),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\CDA\repos\brand-compass-frontend-template\apps\collect-cms\src\contexts\NavigationContext.tsx#NavigationContext`);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\CDA\repos\brand-compass-frontend-template\apps\collect-cms\src\contexts\NavigationContext.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/mock/Navigation.ts":
/*!********************************!*\
  !*** ./src/mock/Navigation.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavigationData: () => (/* binding */ NavigationData)\n/* harmony export */ });\nconst NavigationData = [\n    {\n        apiId: \"test-really-long-string-page-group\",\n        uid: \"test-really-long-string-page-group\",\n        isDisplayed: true,\n        isPinned: true,\n        info: {\n            displayName: \"Test Page Group\",\n            description: \"Test Page Group\"\n        },\n        kind: \"group\",\n        layouts: [\n            {\n                name: \"Test Page\",\n                apiId: \"test-page-123\",\n                uid: \"test-page\",\n                visible: true,\n                kind: \"singleType\",\n                identifierField: \"slug\",\n                defaultMode: \"editor\"\n            },\n            {\n                name: \"Test\",\n                apiId: \"test\",\n                uid: \"test\",\n                visible: true,\n                kind: \"collectionType\",\n                identifierField: \"slug\",\n                defaultMode: \"builder\"\n            }\n        ]\n    },\n    {\n        apiId: \"test-page\",\n        uid: \"test-page\",\n        isDisplayed: true,\n        isPinned: true,\n        info: {\n            displayName: \"Test Page\",\n            description: \"Test Page\"\n        },\n        kind: \"group\",\n        layouts: [\n            {\n                name: \"Test Page\",\n                apiId: \"test-page-123\",\n                uid: \"test-page\",\n                visible: true,\n                kind: \"singleType\",\n                identifierField: \"slug\",\n                defaultMode: \"editor\"\n            }\n        ]\n    },\n    {\n        apiId: \"page\",\n        uid: \"page-group\",\n        isDisplayed: true,\n        isPinned: false,\n        info: {\n            displayName: \"Page\",\n            description: \"Page\"\n        },\n        kind: \"group\",\n        layouts: [\n            {\n                name: \"Page\",\n                apiId: \"page\",\n                uid: \"page\",\n                visible: true,\n                kind: \"collectionType\",\n                identifierField: \"slug\",\n                defaultMode: \"builder\"\n            }\n        ]\n    },\n    {\n        apiId: \"other\",\n        uid: \"other\",\n        isDisplayed: true,\n        isPinned: false,\n        info: {\n            displayName: \"Other\",\n            description: \"Other\"\n        },\n        kind: \"group\",\n        layouts: [\n            {\n                name: \"Home Page\",\n                apiId: \"home-page\",\n                uid: \"home-page\",\n                visible: true,\n                kind: \"singleType\",\n                identifierField: \"slug\",\n                defaultMode: \"editor\"\n            },\n            {\n                name: \"Test Page 2\",\n                apiId: \"test-page-2\",\n                uid: \"test-page\",\n                visible: true,\n                kind: \"singleType\",\n                identifierField: \"slug\",\n                defaultMode: \"builder\"\n            },\n            {\n                name: \"Test\",\n                apiId: \"test\",\n                uid: \"test\",\n                visible: true,\n                kind: \"collectionType\",\n                identifierField: \"slug\",\n                defaultMode: \"builder\"\n            },\n            {\n                name: \"Category\",\n                apiId: \"category\",\n                uid: \"category\",\n                visible: true,\n                kind: \"collectionType\",\n                identifierField: \"slug\",\n                defaultMode: \"editor\"\n            }\n        ]\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/mock/Navigation.ts\n");

/***/ }),

/***/ "(rsc)/../../packages/i18n/src/settings/index.ts":
/*!*************************************************!*\
  !*** ../../packages/i18n/src/settings/index.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultNS: () => (/* binding */ defaultNS),\n/* harmony export */   fallbackLng: () => (/* binding */ fallbackLng),\n/* harmony export */   getOptions: () => (/* binding */ getOptions),\n/* harmony export */   languages: () => (/* binding */ languages)\n/* harmony export */ });\n/* eslint-disable @typescript-eslint/naming-convention */ const fallbackLng = \"en\";\nconst languages = [\n    fallbackLng\n];\nconst defaultNS = \"common\";\nfunction getOptions(lng = fallbackLng, ns = defaultNS) {\n    return {\n        // debug: true,\n        supportedLngs: languages,\n        preload: languages,\n        fallbackLng,\n        lng,\n        fallbackNS: defaultNS,\n        defaultNS: Array.isArray(ns) ? ns[0] : ns,\n        ns\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vcGFja2FnZXMvaTE4bi9zcmMvc2V0dGluZ3MvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLHVEQUF1RCxHQUNoRCxNQUFNQSxjQUFjLEtBQUk7QUFDeEIsTUFBTUMsWUFBWTtJQUFDRDtDQUFZO0FBQy9CLE1BQU1FLFlBQVksU0FBUTtBQUUxQixTQUFTQyxXQUFXQyxNQUFNSixXQUFXLEVBQUVLLEtBQXdCSCxTQUFTO0lBQzlFLE9BQU87UUFDTixlQUFlO1FBQ2ZJLGVBQWVMO1FBQ2ZNLFNBQVNOO1FBQ1REO1FBQ0FJO1FBQ0FJLFlBQVlOO1FBQ1pBLFdBQVdPLE1BQU1DLE9BQU8sQ0FBQ0wsTUFBTUEsRUFBRSxDQUFDLEVBQUUsR0FBR0E7UUFDdkNBO0lBQ0Q7QUFDRCIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbGxlY3QtY21zLy4uLy4uL3BhY2thZ2VzL2kxOG4vc3JjL3NldHRpbmdzL2luZGV4LnRzPzU1M2UiXSwic291cmNlc0NvbnRlbnQiOlsiLyogZXNsaW50LWRpc2FibGUgQHR5cGVzY3JpcHQtZXNsaW50L25hbWluZy1jb252ZW50aW9uICovXG5leHBvcnQgY29uc3QgZmFsbGJhY2tMbmcgPSAnZW4nXG5leHBvcnQgY29uc3QgbGFuZ3VhZ2VzID0gW2ZhbGxiYWNrTG5nXVxuZXhwb3J0IGNvbnN0IGRlZmF1bHROUyA9ICdjb21tb24nXG5cbmV4cG9ydCBmdW5jdGlvbiBnZXRPcHRpb25zKGxuZyA9IGZhbGxiYWNrTG5nLCBuczogc3RyaW5nIHwgc3RyaW5nW10gPSBkZWZhdWx0TlMpIHtcblx0cmV0dXJuIHtcblx0XHQvLyBkZWJ1ZzogdHJ1ZSxcblx0XHRzdXBwb3J0ZWRMbmdzOiBsYW5ndWFnZXMsXG5cdFx0cHJlbG9hZDogbGFuZ3VhZ2VzLFxuXHRcdGZhbGxiYWNrTG5nLFxuXHRcdGxuZyxcblx0XHRmYWxsYmFja05TOiBkZWZhdWx0TlMsXG5cdFx0ZGVmYXVsdE5TOiBBcnJheS5pc0FycmF5KG5zKSA/IG5zWzBdIDogbnMsXG5cdFx0bnMsXG5cdH1cbn1cbiJdLCJuYW1lcyI6WyJmYWxsYmFja0xuZyIsImxhbmd1YWdlcyIsImRlZmF1bHROUyIsImdldE9wdGlvbnMiLCJsbmciLCJucyIsInN1cHBvcnRlZExuZ3MiLCJwcmVsb2FkIiwiZmFsbGJhY2tOUyIsIkFycmF5IiwiaXNBcnJheSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../packages/i18n/src/settings/index.ts\n");

/***/ }),

/***/ "(rsc)/../../packages/ui-lib/src/contexts/GeneralSettingContext.tsx":
/*!********************************************************************!*\
  !*** ../../packages/ui-lib/src/contexts/GeneralSettingContext.tsx ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GeneralSettingContext: () => (/* binding */ e0),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\CDA\repos\brand-compass-frontend-template\packages\ui-lib\src\contexts\GeneralSettingContext.tsx#GeneralSettingContext`);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\CDA\repos\brand-compass-frontend-template\packages\ui-lib\src\contexts\GeneralSettingContext.tsx#default`));


/***/ }),

/***/ "(rsc)/../../packages/ui-lib/src/contexts/NavigationContext.tsx":
/*!****************************************************************!*\
  !*** ../../packages/ui-lib/src/contexts/NavigationContext.tsx ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NavigationContext: () => (/* binding */ e0),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\CDA\repos\brand-compass-frontend-template\packages\ui-lib\src\contexts\NavigationContext.tsx#NavigationContext`);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\CDA\repos\brand-compass-frontend-template\packages\ui-lib\src\contexts\NavigationContext.tsx#default`));


/***/ }),

/***/ "(rsc)/../../packages/ui-lib/src/contexts/QuickNavigationContext.tsx":
/*!*********************************************************************!*\
  !*** ../../packages/ui-lib/src/contexts/QuickNavigationContext.tsx ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   QuickNavigationContext: () => (/* binding */ e0),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\CDA\repos\brand-compass-frontend-template\packages\ui-lib\src\contexts\QuickNavigationContext.tsx#QuickNavigationContext`);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\CDA\repos\brand-compass-frontend-template\packages\ui-lib\src\contexts\QuickNavigationContext.tsx#default`));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb2xsZWN0LWNtcy8uL3NyYy9hcHAvZmF2aWNvbi5pY28/ZGI0NSJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(ssr)/../../packages/core/dist/utils/cookies.js":
/*!*************************************************!*\
  !*** ../../packages/core/dist/utils/cookies.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCookie: () => (/* binding */ getCookie),\n/* harmony export */   setCookie: () => (/* binding */ setCookie)\n/* harmony export */ });\nfunction setCookie(cname, cvalue, exdays) {\n    if (true) {\n        return;\n    }\n    const d = new Date();\n    d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000);\n    const expires = `expires=${d.toUTCString()}`;\n    document.cookie = `${cname}=${cvalue};${expires};path=/`;\n}\nfunction getCookie(cname) {\n    if (true) {\n        return null;\n    }\n    const name = cname + \"=\";\n    const ca = document.cookie.split(\";\");\n    for(let i = 0; i < ca.length; i++){\n        let c = ca[i];\n        if (!c) {\n            continue;\n        }\n        while(c.charAt(0) == \" \"){\n            c = c.substring(1);\n        }\n        if (c.indexOf(name) == 0) {\n            return c.substring(name.length, c.length);\n        }\n    }\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/core/dist/utils/cookies.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/i18next","vendor-chunks/react-i18next","vendor-chunks/i18next-browser-languagedetector","vendor-chunks/html-parse-stringify","vendor-chunks/i18next-resources-to-backend","vendor-chunks/void-elements"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blng%5D%2F%5B...rest%5D%2Fpage&page=%2F%5Blng%5D%2F%5B...rest%5D%2Fpage&appPaths=%2F%5Blng%5D%2F%5B...rest%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blng%5D%2F%5B...rest%5D%2Fpage.tsx&appDir=D%3A%5CCDA%5Crepos%5Cbrand-compass-frontend-template%5Capps%5Ccollect-cms%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCDA%5Crepos%5Cbrand-compass-frontend-template%5Capps%5Ccollect-cms&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();